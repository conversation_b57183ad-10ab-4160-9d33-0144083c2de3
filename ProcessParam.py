import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    raw_param_df = pd.read_excel('ADS_V_CF_PDS_RESULT.xlsx')
    param_mapping_df = pd.read_excel('制程参数名称.xlsx')

# 1.raw_param_df 去重
# 同一 PRODUCT_ID/STEP_ID/GLASS_ID/PARAM_NAME 只保留GLASS_START_TIME最新的一笔
def deduplicate_raw_data(df):
    """去重：保留每个组合的最新记录"""
    return df.sort_values('GLASS_START_TIME').drop_duplicates(
        subset=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME'],
        keep='last'
    )

# 2.重命名PARAM_NAME
# 根据param_mapping_df中的PARAM_NAME/SECONDARY_NAME重命名raw_param_df中的PARAM_NAME
def rename_param_names(df, mapping_df):
    """重命名参数名称"""
    # 创建映射字典
    name_mapping = dict(zip(mapping_df['PARAM_NAME'], mapping_df['SECONDARY_NAME']))
    # 只重命名存在映射的参数
    df = df.copy()
    df['PARAM_NAME'] = df['PARAM_NAME'].map(name_mapping).fillna(df['PARAM_NAME'])
    return df

# 3.转置
# PRODUCT_ID/STEP_ID/GLASS_ID作为键，转置PARAM_NAME栏位，值为PARAM_VALUE(如果PARAM_VALUE为空则取STR_VALUE)
def pivot_data(df):
    """转置数据：以PRODUCT_ID/STEP_ID/GLASS_ID为键"""
    # 处理值：PARAM_VALUE为空时使用STR_VALUE
    df = df.copy()
    df['VALUE'] = df['PARAM_VALUE'].fillna(df['STR_VALUE'])

    # 转置
    pivoted = df.pivot_table(
        index=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID'],
        columns='PARAM_NAME',
        values='VALUE',
        aggfunc='first'  # 如果有重复，取第一个
    ).reset_index()

    # 展平列名
    pivoted.columns.name = None
    return pivoted

# 4.根据param_mapping_df进行数据操作
def apply_operations(df, mapping_df, raw_df):
    """根据映射表进行数据操作"""
    result_df = df.copy()
    groups = {}  # 存储分组信息

    for _, row in mapping_df.iterrows():
        operation = row['OPERATION']
        param_name = row['PARAM_NAME']
        secondary_name = row['SECONDARY_NAME']
        operation_params = row['OPERATION_PARAMETERS']

        if operation == 'get_time':
            # 获取指定参数的GLASS_START_TIME
            time_mapping = raw_df[raw_df['PARAM_NAME'] == operation_params].set_index(
                ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID']
            )['GLASS_START_TIME']
            result_df[secondary_name] = result_df.set_index(
                ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID']
            ).index.map(time_mapping)

        elif operation == 'group':
            # 记录分组信息，为后续mean操作准备
            if operation_params not in result_df.columns:
                continue
            groups[secondary_name] = result_df[operation_params]

        elif operation == 'mean':
            # 计算指定组的均值
            if operation_params in groups:
                result_df[secondary_name] = groups[operation_params]

        elif operation == 'map':
            # 根据映射表进行值映射
            # 这里需要根据具体的映射表实现
            # 假设映射表在同一目录下，文件名为operation_params
            try:
                map_df = pd.read_excel(f'{operation_params}.xlsx')
                if len(map_df.columns) >= 2:
                    map_dict = dict(zip(map_df.iloc[:, 0], map_df.iloc[:, 1]))
                    if param_name in result_df.columns:
                        result_df[secondary_name] = result_df[param_name].map(map_dict)
                        # 不保留原列
                        result_df = result_df.drop(columns=[param_name])
            except:
                print(f"Warning: Could not load mapping file {operation_params}.xlsx")

    return result_df

# 5.显式定义类型
def convert_dtypes(df, mapping_df):
    """显式转换数据类型"""
    result_df = df.copy()

    for _, row in mapping_df.iterrows():
        secondary_name = row['SECONDARY_NAME']
        dtype = row['DTYPE']

        if secondary_name in result_df.columns and pd.notna(dtype):
            try:
                if dtype.lower() == 'int':
                    result_df[secondary_name] = pd.to_numeric(result_df[secondary_name], errors='coerce').astype('Int64')
                elif dtype.lower() == 'float':
                    result_df[secondary_name] = pd.to_numeric(result_df[secondary_name], errors='coerce')
                elif dtype.lower() == 'str':
                    result_df[secondary_name] = result_df[secondary_name].astype(str)
                elif dtype.lower() == 'datetime':
                    result_df[secondary_name] = pd.to_datetime(result_df[secondary_name], errors='coerce')
            except Exception as e:
                print(f"Warning: Could not convert {secondary_name} to {dtype}: {e}")

    return result_df

# 主处理流程
def process_data():
    """主数据处理流程"""
    if platform == 'ide':
        # 步骤1：去重
        deduped_df = deduplicate_raw_data(raw_param_df)

        # 步骤2：重命名
        renamed_df = rename_param_names(deduped_df, param_mapping_df)

        # 步骤3：转置
        pivoted_df = pivot_data(renamed_df)

        # 步骤4：数据操作
        operated_df = apply_operations(pivoted_df, param_mapping_df, deduped_df)

        # 步骤5：类型转换
        final_df = convert_dtypes(operated_df, param_mapping_df)

        return final_df
    else:
        print("Spotfire platform detected - implement spotfire-specific logic here")
        return None

# 执行处理
if __name__ == "__main__":
    processed_df = process_data()
    if processed_df is not None:
        print("数据处理完成")
        print(f"最终数据形状: {processed_df.shape}")
        print(f"列名: {list(processed_df.columns)}")

        # 可选：保存结果
        processed_df.to_excel('processed_result.xlsx', index=False)
