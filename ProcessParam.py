import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    raw_param_df = pd.read_excel('ADS_V_CF_PDS_RESULT.xlsx')
    param_mapping_df = pd.read_excel('制程参数名称.xlsx')

# 1.raw_param_df 去重
# 同一 PRODUCT_ID/STEP_ID/GLASS_ID/PARAM_NAME 只保留GLASS_START_TIME最新的一笔

# 2.重命名PARAM_NAME
# 根据param_mapping_df中的PARAM_NAME/SECONDARY_NAME重命名raw_param_df中的PARAM_NAME


# 3.转置
# PRODUCT_ID/STEP_ID/GLASS_ID作为键，转置PARAM_NAME栏位，值为PARAM_VALUE(如果PARAM_VALUE为空则取STR_VALUE)

# 4.根据param_mapping_df进行数据操作
# get_time:获取该键对应的参数的GLASS_START_TIME
#