import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'
    raw_param_df = pd.read_excel('ADS_V_CF_PDS_RESULT.xlsx')
    param_mapping_df = pd.read_excel('制程参数名称.xlsx')

# 1.raw_param_df 去重
# 同一 PRODUCT_ID/STEP_ID/GLASS_ID/PARAM_NAME 只保留GLASS_START_TIME最新的一笔
def deduplicate_raw_data(df):
    """去重：保留每个组合的最新记录"""
    return df.sort_values('GLASS_START_TIME').drop_duplicates(
        subset=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME'],
        keep='last'
    )

# 2.重命名PARAM_NAME
# 根据param_mapping_df中的PARAM_NAME/SECONDARY_NAME重命名raw_param_df中的PARAM_NAME
def rename_param_names(df, mapping_df):
    """重命名参数名称"""
    # 创建映射字典
    name_mapping = dict(zip(mapping_df['PARAM_NAME'], mapping_df['SECONDARY_NAME']))
    # 只重命名存在映射的参数
    df = df.copy()
    df['PARAM_NAME'] = df['PARAM_NAME'].map(name_mapping).fillna(df['PARAM_NAME'])
    return df

# 3.转置
# PRODUCT_ID/STEP_ID/GLASS_ID作为键，转置PARAM_NAME栏位，值为PARAM_VALUE(如果PARAM_VALUE为空则取STR_VALUE)
def pivot_data(df):
    """转置数据：以PRODUCT_ID/STEP_ID/GLASS_ID为键"""
    # 处理值：PARAM_VALUE为空时使用STR_VALUE
    df = df.copy()
    df['VALUE'] = df['PARAM_VALUE'].fillna(df['STR_VALUE'])

    # 转置
    pivoted = df.pivot_table(
        index=['PRODUCT_ID', 'STEP_ID', 'GLASS_ID'],
        columns='PARAM_NAME',
        values='VALUE',
        aggfunc='first'  # 如果有重复，取第一个
    ).reset_index()

    # 展平列名
    pivoted.columns.name = None
    return pivoted

# 4.根据param_mapping_df进行数据操作
def apply_operations(df, mapping_df, deduped_raw_df):
    """根据映射表进行数据操作，使用去重后的原始数据"""
    result_df = df.copy()

    # 先收集所有group定义和mean操作，避免顺序依赖
    groups = {}  # 存储分组信息: {group_name: [column_names]}
    mean_operations = []  # 存储mean操作，稍后处理

    for _, row in mapping_df.iterrows():
        operation = row['OPERATION']
        param_name = row['PARAM_NAME']
        secondary_name = row['SECONDARY_NAME']
        operation_params = row['OPERATION_PARAMETERS']

        if operation == 'get_time':
            # 获取指定参数的GLASS_START_TIME，使用去重后的数据
            time_mapping = deduped_raw_df[deduped_raw_df['PARAM_NAME'] == operation_params].set_index(
                ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID']
            )['GLASS_START_TIME']
            result_df[secondary_name] = result_df.set_index(
                ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID']
            ).index.map(time_mapping)

        elif operation == 'group':
            # group操作：将PARAM_NAME列添加到OPERATION_PARAMETERS指定的组中，不创建新列
            if param_name in result_df.columns:
                group_name = operation_params  # 组名来自OPERATION_PARAMETERS
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(param_name)

        elif operation == 'mean':
            # 暂存mean操作，等所有group处理完后再执行
            mean_operations.append((secondary_name, operation_params))

        elif operation == 'map':
            # 根据OPERATION_PARAMETERS中的字典进行值映射
            try:
                # 解析字典字符串，例如: {1:"Off", 2:"On"}
                import ast
                map_dict = ast.literal_eval(operation_params)
                if param_name in result_df.columns:
                    result_df[secondary_name] = result_df[param_name].map(map_dict)
                    # 不保留原列
                    result_df = result_df.drop(columns=[param_name])
            except Exception as e:
                print(f"Warning: Could not parse mapping dictionary {operation_params}: {e}")

    # 第二遍：处理mean操作
    for secondary_name, group_name in mean_operations:
        if group_name in groups:
            # 计算该组所有列的均值
            group_columns = groups[group_name]
            available_columns = [col for col in group_columns if col in result_df.columns]
            if available_columns:
                # 对数值列计算均值
                numeric_data = result_df[available_columns].apply(pd.to_numeric, errors='coerce')
                result_df[secondary_name] = numeric_data.mean(axis=1)
            else:
                print(f"Warning: No available columns found for group '{group_name}'")
        else:
            print(f"Warning: Group '{group_name}' not found for mean operation '{secondary_name}'")

    return result_df

# 5.显式定义类型
def convert_dtypes(df, mapping_df):
    """显式转换数据类型"""
    result_df = df.copy()

    for _, row in mapping_df.iterrows():
        secondary_name = row['SECONDARY_NAME']
        dtype = row['DTYPE']

        if secondary_name in result_df.columns and pd.notna(dtype):
            try:
                if dtype.lower() == 'int':
                    result_df[secondary_name] = pd.to_numeric(result_df[secondary_name], errors='coerce').astype('Int64')
                elif dtype.lower() == 'float':
                    result_df[secondary_name] = pd.to_numeric(result_df[secondary_name], errors='coerce')
                elif dtype.lower() == 'str':
                    result_df[secondary_name] = result_df[secondary_name].astype(str)
                elif dtype.lower() == 'datetime':
                    result_df[secondary_name] = pd.to_datetime(result_df[secondary_name], errors='coerce')
            except Exception as e:
                print(f"Warning: Could not convert {secondary_name} to {dtype}: {e}")

    return result_df

# 6.重新排列列顺序并排序
def reorder_and_sort(df, mapping_df):
    """根据SECONDARY_NAME顺序重排列，并按涂布时间升序排序"""
    # 获取所有SECONDARY_NAME的顺序
    secondary_names = mapping_df['SECONDARY_NAME'].dropna().tolist()

    # 保留原有的主键列
    key_columns = ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID']

    # 构建最终列顺序：主键列 + 按mapping_df顺序的SECONDARY_NAME列
    final_columns = key_columns.copy()
    for col in secondary_names:
        if col in df.columns and col not in final_columns:
            final_columns.append(col)

    # 添加其他未在mapping中定义的列
    for col in df.columns:
        if col not in final_columns:
            final_columns.append(col)

    # 重排列顺序
    result_df = df[final_columns].copy()

    # 按涂布时间升序排序
    if '涂布时间' in result_df.columns:
        result_df = result_df.sort_values('涂布时间', ascending=True)
    else:
        print("Warning: '涂布时间' column not found for sorting")

    return result_df

# 主处理流程
def process_data():
    """主数据处理流程"""
    if platform == 'ide':
        # 步骤1：去重
        deduped_df = deduplicate_raw_data(raw_param_df)

        # 步骤2：重命名
        renamed_df = rename_param_names(deduped_df, param_mapping_df)

        # 步骤3：转置
        pivoted_df = pivot_data(renamed_df)

        # 步骤4：数据操作
        operated_df = apply_operations(pivoted_df, param_mapping_df, deduped_df)

        # 步骤5：类型转换
        typed_df = convert_dtypes(operated_df, param_mapping_df)

        # 步骤6：重排列顺序并排序
        final_df = reorder_and_sort(typed_df, param_mapping_df)

        return final_df
    else:
        print("Spotfire platform detected - implement spotfire-specific logic here")
        return None

# 执行处理
if __name__ == "__main__":
    processed_df = process_data()
    if processed_df is not None:
        print("数据处理完成")
        print(f"最终数据形状: {processed_df.shape}")
        print(f"列名: {list(processed_df.columns)}")

        # 可选：保存结果
        processed_df.to_excel('processed_result.xlsx', index=False)
